import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Home, Sparkles, Clock, Calendar, CheckCircle, 
  Users, TrendingUp, Shield, Star, ArrowRight,
  Zap, Gift, MessageCircle, ThumbsUp
} from 'lucide-react';
import { calculatePrice, type PricingInput } from '../../../../lib/services/pricingService';

interface FormData {
  homeType: string;
  homeSize: string;
  rooms: { bedrooms: number; bathrooms: number };
  frequency: string;
  addOns: string[];
  preferredDay: string;
  preferredTime: string;
  contactInfo: {
    name: string;
    email: string;
    phone: string;
    address: string;
  };
}

interface LiveActivity {
  id: string;
  message: string;
  timestamp: Date;
  type: 'booking' | 'review' | 'special';
}

const ConversationalCleaningForm: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [isTyping, setIsTyping] = useState(false);
  const [showRecommendation, setShowRecommendation] = useState(false);
  const [liveActivities, setLiveActivities] = useState<LiveActivity[]>([]);
  const [formData, setFormData] = useState<FormData>({
    homeType: '',
    homeSize: '',
    rooms: { bedrooms: 2, bathrooms: 1 },
    frequency: '',
    addOns: [],
    preferredDay: '',
    preferredTime: '',
    contactInfo: {
      name: '',
      email: '',
      phone: '',
      address: ''
    }
  });

  // Simulate live activities
  useEffect(() => {
    const activities = [
      { id: '1', message: 'Sarah from Downtown just booked weekly cleaning', timestamp: new Date(), type: 'booking' as const },
      { id: '2', message: 'Mike saved $240/year with bi-weekly service', timestamp: new Date(), type: 'special' as const },
      { id: '3', message: '⭐⭐⭐⭐⭐ "Best cleaning service!" - Jennifer', timestamp: new Date(), type: 'review' as const }
    ];

    const interval = setInterval(() => {
      const randomActivity = activities[Math.floor(Math.random() * activities.length)];
      setLiveActivities(prev => [{ ...randomActivity, id: Date.now().toString() }, ...prev].slice(0, 3));
    }, 8000);

    return () => clearInterval(interval);
  }, []);

  // Calculate dynamic pricing using centralized service
  const calculateFormPrice = () => {
    try {
      // Convert home size to square footage
      const sizeToSqft: Record<string, number> = {
        'studio': 500,
        '1bed': 800,
        '2bed': 1200,
        '3bed': 1600,
        '4bed+': 2200
      };

      const pricingInput: PricingInput = {
        serviceType: 'residential_regular',
        propertySize: sizeToSqft[formData.homeSize] || 1200,
        frequency: formData.frequency === 'biweekly' ? 'biweekly' :
                  formData.frequency === 'onetime' ? 'onetime' :
                  formData.frequency || 'onetime',
        addOns: formData.addOns || [],
        customOptions: {
          bedrooms: formData.rooms.bedrooms || 0,
          bathrooms: formData.rooms.bathrooms || 0,
          propertyType: formData.homeType || 'house'
        }
      };

      const pricingResult = calculatePrice(pricingInput);
      return Math.round(pricingResult.total);

    } catch (error) {
      console.error('Error calculating price:', error);
      return 120; // Fallback price
    }
  };

  const homeTypes = [
    { id: 'apartment', name: 'Apartment', icon: '🏢', popular: true },
    { id: 'house', name: 'House', icon: '🏠', popular: false },
    { id: 'condo', name: 'Condo', icon: '🏙️', popular: false },
    { id: 'townhouse', name: 'Townhouse', icon: '🏘️', popular: false }
  ];

  const homeSizes = [
    { id: 'studio', name: 'Studio', rooms: '0-1 BR', price: '$80', icon: '🏠' },
    { id: '1bed', name: '1 Bedroom', rooms: '1 BR', price: '$100', icon: '🏡' },
    { id: '2bed', name: '2 Bedroom', rooms: '2 BR', price: '$120', icon: '🏘️', popular: true },
    { id: '3bed', name: '3 Bedroom', rooms: '3 BR', price: '$150', icon: '🏛️' },
    { id: '4bed+', name: '4+ Bedroom', rooms: '4+ BR', price: '$180', icon: '🏰' }
  ];

  const frequencies = [
    { id: 'weekly', name: 'Weekly', discount: '15% OFF', description: 'Maximum cleanliness', icon: '🌟' },
    { id: 'biweekly', name: 'Bi-Weekly', discount: '10% OFF', description: 'Perfect balance', icon: '✨', popular: true },
    { id: 'monthly', name: 'Monthly', discount: 'Standard', description: 'Basic maintenance', icon: '🧹' },
    { id: 'onetime', name: 'One-Time', discount: 'No discount', description: 'Deep clean', icon: '🎯' }
  ];

  const addOns = [
    { id: 'inside-oven', name: 'Inside Oven', price: '+$20', icon: '🔥' },
    { id: 'inside-fridge', name: 'Inside Fridge', price: '+$20', icon: '❄️' },
    { id: 'laundry', name: 'Laundry', price: '+$20', icon: '👕' },
    { id: 'windows', name: 'Interior Windows', price: '+$20', icon: '🪟' }
  ];

  const timeSlots = [
    { id: 'morning', name: '8AM - 12PM', icon: '🌅', available: true },
    { id: 'afternoon', name: '12PM - 4PM', icon: '☀️', available: true, popular: true },
    { id: 'evening', name: '4PM - 7PM', icon: '🌆', available: false }
  ];

  const handleNext = () => {
    setIsTyping(true);
    setTimeout(() => {
      setIsTyping(false);
      setCurrentStep(currentStep + 1);
      if (currentStep === 1) {
        setShowRecommendation(true);
      }
    }, 800);
  };

  const handleSubmit = async () => {
    try {
      // Save to localStorage for persistence
      localStorage.setItem('residentialBookingData', JSON.stringify({
        ...formData,
        totalPrice: calculateFormPrice(),
        serviceType: 'residential_regular', // Use database-compatible service type
        serviceDisplayName: 'Regular House Cleaning', // Keep display name for UI
        submittedAt: new Date().toISOString()
      }));

      // Navigate to payment or thank you page
      navigate('/thank-you');
    } catch (error) {
      console.error('Error submitting form:', error);
      // Handle error appropriately
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Live Activity Feed */}
      <AnimatePresence>
        <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
          {liveActivities.map((activity) => (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 100 }}
              className="bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full shadow-lg flex items-center gap-2"
            >
              {activity.type === 'booking' && <Users className="w-4 h-4 text-blue-500" />}
              {activity.type === 'review' && <Star className="w-4 h-4 text-yellow-500" />}
              {activity.type === 'special' && <Gift className="w-4 h-4 text-green-500" />}
              <span className="text-sm font-medium">{activity.message}</span>
            </motion.div>
          ))}
        </div>
      </AnimatePresence>

      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header with Progress */}
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              >
                <Sparkles className="w-8 h-8 text-blue-600" />
              </motion.div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                CleanPro Booking Assistant
              </h1>
            </div>
            <div className="flex items-center gap-2">
              <Shield className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium">Secure Booking</span>
            </div>
          </div>

          {/* Visual Progress Bar */}
          <div className="relative h-2 bg-gray-200 rounded-full overflow-hidden">
            <motion.div
              className="absolute inset-y-0 left-0 bg-gradient-to-r from-blue-500 to-purple-500"
              initial={{ width: '0%' }}
              animate={{ width: `${((currentStep + 1) / 5) * 100}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </motion.div>

        {/* Conversational Interface */}
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <AnimatePresence mode="wait">
            {/* Step 0: Welcome & Home Type */}
            {currentStep === 0 && (
              <motion.div
                key="step0"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="space-y-6"
              >
                <div className="text-center mb-8">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring", delay: 0.2 }}
                    className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full mb-4"
                  >
                    <MessageCircle className="w-10 h-10 text-blue-600" />
                  </motion.div>
                  <h2 className="text-2xl font-bold mb-2">Hi! Let's get your home sparkling clean 🏠✨</h2>
                  <p className="text-gray-600">First, what type of home do you have?</p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {homeTypes.map((type, index) => (
                    <motion.button
                      key={type.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      onClick={() => {
                        setFormData({ ...formData, homeType: type.id });
                        handleNext();
                      }}
                      className={`relative p-6 rounded-xl border-2 transition-all hover:shadow-lg ${
                        formData.homeType === type.id 
                          ? 'border-blue-500 bg-blue-50' 
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      {type.popular && (
                        <span className="absolute -top-2 -right-2 bg-gradient-to-r from-orange-500 to-pink-500 text-white text-xs px-2 py-1 rounded-full">
                          Popular
                        </span>
                      )}
                      <div className="text-4xl mb-2">{type.icon}</div>
                      <div className="font-semibold">{type.name}</div>
                    </motion.button>
                  ))}
                </div>

                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="flex items-center justify-center gap-2 text-sm text-gray-500"
                >
                  <Users className="w-4 h-4" />
                  <span>Join 10,000+ happy customers</span>
                </motion.div>
              </motion.div>
            )}

            {/* Step 1: Home Size with Smart Recommendation */}
            {currentStep === 1 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="space-y-6"
              >
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold mb-2">Perfect! Now, how big is your {formData.homeType}?</h2>
                  <p className="text-gray-600">This helps us estimate the time needed</p>
                </div>

                {showRecommendation && (
                  <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-xl mb-6"
                  >
                    <div className="flex items-center gap-3">
                      <Zap className="w-5 h-5 text-blue-600" />
                      <span className="font-medium">
                        Based on your {formData.homeType}, most customers choose 2 Bedroom service
                      </span>
                    </div>
                  </motion.div>
                )}

                <div className="space-y-3">
                  {homeSizes.map((size, index) => (
                    <motion.button
                      key={size.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      onClick={() => {
                        setFormData({ ...formData, homeSize: size.id });
                        handleNext();
                      }}
                      className={`w-full p-4 rounded-xl border-2 transition-all hover:shadow-md flex items-center justify-between ${
                        formData.homeSize === size.id 
                          ? 'border-blue-500 bg-blue-50' 
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center gap-4">
                        <span className="text-2xl">{size.icon}</span>
                        <div className="text-left">
                          <div className="font-semibold flex items-center gap-2">
                            {size.name}
                            {size.popular && (
                              <span className="bg-blue-100 text-blue-700 text-xs px-2 py-0.5 rounded-full">
                                Most Popular
                              </span>
                            )}
                          </div>
                          <div className="text-sm text-gray-500">{size.rooms}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-lg">{size.price}</div>
                        <div className="text-xs text-gray-500">Starting at</div>
                      </div>
                    </motion.button>
                  ))}
                </div>

                {isTyping && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="flex items-center gap-2 text-gray-500"
                  >
                    <div className="flex gap-1">
                      <span className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                      <span className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                      <span className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                    </div>
                    <span className="text-sm">Assistant is typing...</span>
                  </motion.div>
                )}
              </motion.div>
            )}

            {/* Step 2: Frequency with Savings Display */}
            {currentStep === 2 && (
              <motion.div
                key="step2"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="space-y-6"
              >
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold mb-2">How often would you like us to clean?</h2>
                  <p className="text-gray-600">Save more with regular service!</p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {frequencies.map((freq, index) => (
                    <motion.button
                      key={freq.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      onClick={() => {
                        setFormData({ ...formData, frequency: freq.id });
                        handleNext();
                      }}
                      className={`relative p-6 rounded-xl border-2 transition-all hover:shadow-lg ${
                        formData.frequency === freq.id 
                          ? 'border-blue-500 bg-blue-50' 
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      {freq.popular && (
                        <span className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs px-3 py-1 rounded-full">
                          Most Popular
                        </span>
                      )}
                      <div className="text-3xl mb-2">{freq.icon}</div>
                      <div className="font-bold text-lg mb-1">{freq.name}</div>
                      <div className="text-sm text-gray-600 mb-2">{freq.description}</div>
                      <div className={`font-bold ${freq.id === 'weekly' ? 'text-green-600' : freq.id === 'biweekly' ? 'text-blue-600' : 'text-gray-500'}`}>
                        {freq.discount}
                      </div>
                    </motion.button>
                  ))}
                </div>

                {formData.frequency && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-green-50 p-4 rounded-xl"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <TrendingUp className="w-5 h-5 text-green-600" />
                        <span className="font-medium">
                          Your estimated price: ${calculateFormPrice()}/clean
                        </span>
                      </div>
                      {formData.frequency === 'weekly' && (
                        <span className="text-green-600 font-bold">Save $288/year!</span>
                      )}
                      {formData.frequency === 'biweekly' && (
                        <span className="text-blue-600 font-bold">Save $144/year!</span>
                      )}
                    </div>
                  </motion.div>
                )}
              </motion.div>
            )}

            {/* Step 3: Add-ons & Scheduling */}
            {currentStep === 3 && (
              <motion.div
                key="step3"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="space-y-6"
              >
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold mb-2">Almost done! Any special add-ons?</h2>
                  <p className="text-gray-600">Select any additional services you'd like</p>
                </div>

                <div className="grid grid-cols-2 gap-3 mb-6">
                  {addOns.map((addon, index) => (
                    <motion.button
                      key={addon.id}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.1 }}
                      onClick={() => {
                        const newAddOns = formData.addOns.includes(addon.id)
                          ? formData.addOns.filter(a => a !== addon.id)
                          : [...formData.addOns, addon.id];
                        setFormData({ ...formData, addOns: newAddOns });
                      }}
                      className={`p-4 rounded-xl border-2 transition-all ${
                        formData.addOns.includes(addon.id)
                          ? 'border-blue-500 bg-blue-50' 
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="text-2xl mb-1">{addon.icon}</div>
                      <div className="font-medium text-sm">{addon.name}</div>
                      <div className="text-xs text-gray-500">{addon.price}</div>
                    </motion.button>
                  ))}
                </div>

                <div className="space-y-4">
                  <h3 className="font-bold text-lg">When would you like your first cleaning?</h3>
                  
                  <div className="grid grid-cols-7 gap-2">
                    {[...Array(14)].map((_, i) => {
                      const date = new Date();
                      date.setDate(date.getDate() + i);
                      const isWeekend = date.getDay() === 0 || date.getDay() === 6;
                      const dateStr = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                      
                      return (
                        <motion.button
                          key={i}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: i * 0.02 }}
                          onClick={() => setFormData({ ...formData, preferredDay: dateStr })}
                          className={`p-3 rounded-lg text-sm transition-all ${
                            formData.preferredDay === dateStr
                              ? 'bg-blue-500 text-white' 
                              : isWeekend 
                                ? 'bg-gray-100 hover:bg-gray-200' 
                                : 'bg-white border hover:border-blue-300'
                          }`}
                          disabled={i < 2}
                        >
                          <div className="font-medium">{date.toLocaleDateString('en-US', { weekday: 'short' })}</div>
                          <div className="text-xs">{date.getDate()}</div>
                        </motion.button>
                      );
                    })}
                  </div>

                  <div className="grid grid-cols-3 gap-3">
                    {timeSlots.map((slot) => (
                      <motion.button
                        key={slot.id}
                        whileHover={{ scale: slot.available ? 1.05 : 1 }}
                        whileTap={{ scale: slot.available ? 0.95 : 1 }}
                        onClick={() => slot.available && setFormData({ ...formData, preferredTime: slot.id })}
                        disabled={!slot.available}
                        className={`p-4 rounded-xl transition-all ${
                          formData.preferredTime === slot.id
                            ? 'bg-blue-500 text-white' 
                            : slot.available
                              ? 'bg-white border hover:border-blue-300'
                              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        }`}
                      >
                        <div className="text-2xl mb-1">{slot.icon}</div>
                        <div className="font-medium text-sm">{slot.name}</div>
                        {slot.popular && slot.available && (
                          <div className="text-xs mt-1 opacity-75">Popular time</div>
                        )}
                        {!slot.available && (
                          <div className="text-xs mt-1">Fully booked</div>
                        )}
                      </motion.button>
                    ))}
                  </div>
                </div>

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setCurrentStep(4)}
                  disabled={!formData.preferredDay || !formData.preferredTime}
                  className={`w-full py-4 rounded-xl font-semibold transition-all flex items-center justify-center gap-2 ${
                    formData.preferredDay && formData.preferredTime
                      ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white hover:shadow-lg'
                      : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  Continue to Final Step
                  <ArrowRight className="w-5 h-5" />
                </motion.button>
              </motion.div>
            )}

            {/* Step 4: Contact Info with Trust Signals */}
            {currentStep === 4 && (
              <motion.div
                key="step4"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="space-y-6"
              >
                <div className="text-center mb-8">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring" }}
                    className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4"
                  >
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  </motion.div>
                  <h2 className="text-2xl font-bold mb-2">You're all set! 🎉</h2>
                  <p className="text-gray-600">Just need your contact details to confirm your booking</p>
                </div>

                {/* Booking Summary */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-xl mb-6"
                >
                  <h3 className="font-bold text-lg mb-3">Your Cleaning Service:</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Service Type:</span>
                      <span className="font-medium">Regular Cleaning ({formData.frequency})</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Home Size:</span>
                      <span className="font-medium">{homeSizes.find(s => s.id === formData.homeSize)?.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>First Cleaning:</span>
                      <span className="font-medium">{formData.preferredDay} @ {timeSlots.find(t => t.id === formData.preferredTime)?.name}</span>
                    </div>
                    {formData.addOns.length > 0 && (
                      <div className="flex justify-between">
                        <span>Add-ons:</span>
                        <span className="font-medium">{formData.addOns.length} selected</span>
                      </div>
                    )}
                    <div className="border-t pt-2 mt-2">
                      <div className="flex justify-between text-lg font-bold">
                        <span>Total per Clean:</span>
                        <span className="text-blue-600">${calculateFormPrice()}</span>
                      </div>
                    </div>
                  </div>
                </motion.div>

                <div className="space-y-4">
                  <input
                    type="text"
                    placeholder="Your Name"
                    value={formData.contactInfo.name}
                    onChange={(e) => setFormData({
                      ...formData,
                      contactInfo: { ...formData.contactInfo, name: e.target.value }
                    })}
                    className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:outline-none transition-colors"
                  />
                  
                  <input
                    type="email"
                    placeholder="Email Address"
                    value={formData.contactInfo.email}
                    onChange={(e) => setFormData({
                      ...formData,
                      contactInfo: { ...formData.contactInfo, email: e.target.value }
                    })}
                    className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:outline-none transition-colors"
                  />
                  
                  <input
                    type="tel"
                    placeholder="Phone Number"
                    value={formData.contactInfo.phone}
                    onChange={(e) => setFormData({
                      ...formData,
                      contactInfo: { ...formData.contactInfo, phone: e.target.value }
                    })}
                    className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:outline-none transition-colors"
                  />
                  
                  <input
                    type="text"
                    placeholder="Service Address"
                    value={formData.contactInfo.address}
                    onChange={(e) => setFormData({
                      ...formData,
                      contactInfo: { ...formData.contactInfo, address: e.target.value }
                    })}
                    className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:outline-none transition-colors"
                  />
                </div>

                {/* Trust Signals */}
                <div className="flex items-center justify-center gap-6 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <Shield className="w-4 h-4" />
                    <span>Insured</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <CheckCircle className="w-4 h-4" />
                    <span>Background Checked</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Star className="w-4 h-4" />
                    <span>4.9 Rating</span>
                  </div>
                </div>

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={handleSubmit}
                  disabled={!formData.contactInfo.name || !formData.contactInfo.email || !formData.contactInfo.phone || !formData.contactInfo.address}
                  className={`w-full py-4 rounded-xl font-semibold transition-all flex items-center justify-center gap-2 ${
                    formData.contactInfo.name && formData.contactInfo.email && formData.contactInfo.phone && formData.contactInfo.address
                      ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:shadow-lg'
                      : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  <ThumbsUp className="w-5 h-5" />
                  Confirm Booking - ${calculateFormPrice()}
                </motion.button>

                <p className="text-center text-xs text-gray-500">
                  By booking, you agree to our terms of service and privacy policy
                </p>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Floating Help Button */}
        <motion.button
          className="fixed bottom-8 left-8 bg-blue-600 text-white p-4 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <MessageCircle className="w-6 h-6" />
        </motion.button>
      </div>
    </div>
  );
};

export default ConversationalCleaningForm; 