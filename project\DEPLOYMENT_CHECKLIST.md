# Payment System Deployment Checklist

## 🚀 **Pre-Deployment Checklist**

### ✅ Code Quality
- [ ] All tests passing (`npm test`)
- [ ] No TypeScript errors (`npm run type-check`)
- [ ] Code linting passed (`npm run lint`)
- [ ] Build successful (`npm run build`)

### ✅ Database Preparation
- [ ] Backup current database
- [ ] Review migration script: `supabase/migrations/20250709000001_fix_payment_system_schema.sql`
- [ ] Test migration on staging environment
- [ ] Verify no data loss in migration

### ✅ Environment Configuration
- [ ] All required environment variables present:
  - `VITE_SUPABASE_URL`
  - `VITE_SUPABASE_ANON_KEY`
  - `VITE_SQUARE_ACCESS_TOKEN`
  - `VITE_SQUARE_APPLICATION_ID`
  - `VITE_SQUARE_LOCATION_ID`
  - `SQUARE_WEBHOOK_SIGNATURE_KEY`

### ✅ Testing Validation
- [ ] Payment system integration tests pass
- [ ] Service type normalization works correctly
- [ ] Pricing calculations are accurate
- [ ] Database schema is consistent

## 🔄 **Deployment Steps**

### Step 1: Database Migration
```bash
# 1. Apply database migration
supabase db push

# 2. Verify migration success
supabase db diff

# 3. Check for any constraint violations
# Run validation queries from migration file
```

### Step 2: Code Deployment
```bash
# 1. Install dependencies
npm install

# 2. Run final tests
npm test

# 3. Build application
npm run build

# 4. Deploy to hosting platform
# (Netlify, Vercel, etc.)
```

### Step 3: Edge Functions Update
```bash
# 1. Deploy updated Edge Functions
supabase functions deploy create-payment-link
supabase functions deploy webhook-payment-status
supabase functions deploy check-payment-status

# 2. Verify function deployment
supabase functions list
```

## ✅ **Post-Deployment Validation**

### Immediate Checks (0-15 minutes)
- [ ] Website loads without errors
- [ ] Payment forms display correctly
- [ ] Service type dropdowns show correct options
- [ ] Pricing calculations work on frontend

### Functional Testing (15-30 minutes)
- [ ] Create test booking with each service type:
  - [ ] Residential Regular
  - [ ] Residential Deep
  - [ ] Office Cleaning
  - [ ] Carpet Cleaning
- [ ] Verify pricing calculations are accurate
- [ ] Test payment link creation (use Square sandbox)
- [ ] Check database records are created correctly

### System Integration (30-60 minutes)
- [ ] Run validation script: `node scripts/validate-payment-system.js`
- [ ] Check Supabase logs for errors
- [ ] Verify webhook endpoints are accessible
- [ ] Test payment status updates

### Database Validation
```sql
-- Check service type distribution
SELECT service_type, COUNT(*) FROM booking_forms 
GROUP BY service_type ORDER BY COUNT(*) DESC;

-- Check payment records consistency
SELECT status, COUNT(*) FROM payment_records 
GROUP BY status;

-- Verify no orphaned records
SELECT COUNT(*) FROM payment_records 
WHERE booking_id IS NOT NULL 
AND booking_id NOT IN (SELECT id FROM booking_forms);
```

## 🚨 **Rollback Plan**

If issues are detected:

### Immediate Rollback (Critical Issues)
1. Revert to previous code deployment
2. Restore database from backup if needed
3. Notify team of rollback

### Partial Rollback (Minor Issues)
1. Disable specific features causing issues
2. Apply hotfixes
3. Re-test affected functionality

### Rollback Commands
```bash
# Revert database migration (if needed)
supabase db reset

# Restore from backup
# (Follow your backup restoration process)

# Revert code deployment
# (Follow your hosting platform's rollback process)
```

## 📊 **Monitoring Setup**

### Key Metrics to Track
1. **Payment Success Rate**
   - Target: >95%
   - Alert if drops below 90%

2. **Service Type Validation Errors**
   - Target: <1% of requests
   - Alert on any constraint violations

3. **Pricing Calculation Accuracy**
   - Monitor for pricing discrepancies
   - Alert on calculation errors

4. **Database Performance**
   - Query response times
   - Connection pool usage

### Monitoring Tools
- [ ] Supabase Dashboard alerts configured
- [ ] Square webhook delivery monitoring
- [ ] Application error tracking (Sentry, etc.)
- [ ] Performance monitoring (New Relic, etc.)

## 📞 **Emergency Contacts**

### Technical Issues
- **Database Issues:** Check Supabase dashboard
- **Payment Processing:** Check Square dashboard
- **Application Errors:** Check hosting platform logs

### Escalation Path
1. Check automated monitoring alerts
2. Review deployment logs
3. Run validation script
4. Contact development team if needed

## 📝 **Post-Deployment Documentation**

### Update Documentation
- [ ] Update API documentation
- [ ] Update service type reference
- [ ] Update pricing configuration docs
- [ ] Update troubleshooting guide

### Team Communication
- [ ] Notify team of successful deployment
- [ ] Share validation results
- [ ] Document any issues encountered
- [ ] Schedule follow-up review

## 🎯 **Success Criteria**

Deployment is considered successful when:
- [ ] All tests pass (19/19)
- [ ] Validation script reports 100% success
- [ ] No critical errors in logs
- [ ] Payment flow works end-to-end
- [ ] All service types function correctly
- [ ] Pricing calculations are accurate
- [ ] Database performance is stable

---

**Deployment Date:** _____________  
**Deployed By:** _____________  
**Validation Results:** _____________  
**Status:** ⏳ Pending / ✅ Success / ❌ Failed
