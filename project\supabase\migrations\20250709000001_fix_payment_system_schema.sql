/*
  # Fix Payment System Database Schema Inconsistencies
  
  This migration addresses critical issues in the payment system:
  1. Standardizes service type constraints across all tables
  2. Fixes field mapping mismatches between payment tables
  3. Ensures proper foreign key relationships
  4. Standardizes status values across tables
  5. Adds missing indexes for performance
  
  Evidence: Based on analysis of multiple conflicting migrations and 
  inconsistent field mappings found in payment processing code.
*/

-- ============================================================================
-- STEP 1: Drop conflicting constraints and fix service types
-- ============================================================================

-- Drop all existing service type constraints to start fresh
ALTER TABLE booking_forms DROP CONSTRAINT IF EXISTS valid_service_type;
ALTER TABLE booking_forms DROP CONSTRAINT IF EXISTS booking_forms_service_type_check;
ALTER TABLE booking_forms DROP CONSTRAINT IF EXISTS check_service_type;

-- Drop all existing status constraints to standardize
ALTER TABLE booking_forms DROP CONSTRAINT IF EXISTS valid_status;
ALTER TABLE booking_forms DROP CONSTRAINT IF EXISTS booking_forms_status_check;

-- ============================================================================
-- STEP 2: Standardize service types across the system
-- ============================================================================

-- Create comprehensive service type constraint that matches all current usage
ALTER TABLE booking_forms 
ADD CONSTRAINT booking_forms_service_type_check CHECK (
  service_type IN (
    -- Commercial services
    'office', 'carpet', 'window', 'deep',
    'construction', 'sanitization', 'tile',
    'pressure', 'floor', 'pool', 'chimney',
    'waste-management', 'upholstery', 'move-out', 
    'event', 'post-construction',
    
    -- Residential services (standardized)
    'residential', 'residential_regular', 'residential_deep', 
    'residential_move', 'regular',
    
    -- Legacy support (will be migrated)
    'Regular House Cleaning', 'Deep House Cleaning',
    'Move-in/Move-out Cleaning'
  )
);

-- ============================================================================
-- STEP 3: Standardize status values across all payment-related tables
-- ============================================================================

-- Standardize booking_forms status values
ALTER TABLE booking_forms 
ADD CONSTRAINT booking_forms_status_check CHECK (
  status IN (
    'pending', 'pending_payment', 'confirmed', 
    'in_progress', 'completed', 'cancelled', 'rescheduled'
  )
);

-- Ensure payment_records has consistent status values
ALTER TABLE payment_records DROP CONSTRAINT IF EXISTS payment_records_status_check;
ALTER TABLE payment_records 
ADD CONSTRAINT payment_records_status_check CHECK (
  status IN (
    'pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'
  )
);

-- ============================================================================
-- STEP 4: Fix field mapping inconsistencies
-- ============================================================================

-- Add missing columns to payment_records if they don't exist
ALTER TABLE payment_records 
ADD COLUMN IF NOT EXISTS total_price decimal(10,2),
ADD COLUMN IF NOT EXISTS currency text DEFAULT 'USD',
ADD COLUMN IF NOT EXISTS description text;

-- Add missing columns to booking_forms if they don't exist  
ALTER TABLE booking_forms
ADD COLUMN IF NOT EXISTS total_price decimal(10,2),
ADD COLUMN IF NOT EXISTS payment_status text DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS special_instructions text;

-- Ensure payment_links table has consistent field names
ALTER TABLE payment_links 
ADD COLUMN IF NOT EXISTS square_payment_link_id text;

-- Update payment_links to use consistent naming
UPDATE payment_links 
SET square_payment_link_id = payment_link_id 
WHERE square_payment_link_id IS NULL AND payment_link_id IS NOT NULL;

-- ============================================================================
-- STEP 5: Add proper foreign key relationships and indexes
-- ============================================================================

-- Add indexes for better performance on payment lookups
CREATE INDEX IF NOT EXISTS idx_payment_records_payment_link_id 
ON payment_records(payment_link_id);

CREATE INDEX IF NOT EXISTS idx_payment_records_booking_id 
ON payment_records(booking_id);

CREATE INDEX IF NOT EXISTS idx_payment_records_status 
ON payment_records(status);

CREATE INDEX IF NOT EXISTS idx_booking_forms_status 
ON booking_forms(status);

CREATE INDEX IF NOT EXISTS idx_payment_links_square_payment_link_id 
ON payment_links(square_payment_link_id);

-- ============================================================================
-- STEP 6: Migrate legacy data to standardized format
-- ============================================================================

-- Migrate legacy service type names to standardized format
UPDATE booking_forms 
SET service_type = 'residential_regular' 
WHERE service_type = 'Regular House Cleaning';

UPDATE booking_forms 
SET service_type = 'residential_deep' 
WHERE service_type = 'Deep House Cleaning';

UPDATE booking_forms 
SET service_type = 'residential_move' 
WHERE service_type = 'Move-in/Move-out Cleaning';

-- Ensure any remaining non-standard residential types default to 'residential'
UPDATE booking_forms 
SET service_type = 'residential' 
WHERE service_type NOT IN (
  'office', 'carpet', 'window', 'deep', 'construction', 'sanitization', 
  'tile', 'pressure', 'floor', 'pool', 'chimney', 'waste-management',
  'upholstery', 'move-out', 'event', 'post-construction',
  'residential', 'residential_regular', 'residential_deep', 'residential_move', 'regular'
) AND (service_type LIKE '%residential%' OR service_type LIKE '%house%' OR service_type LIKE '%home%');

-- ============================================================================
-- STEP 7: Add data validation triggers
-- ============================================================================

-- Create function to validate payment data consistency
CREATE OR REPLACE FUNCTION validate_payment_data()
RETURNS TRIGGER AS $$
BEGIN
  -- Ensure amount is positive
  IF NEW.amount <= 0 THEN
    RAISE EXCEPTION 'Payment amount must be positive';
  END IF;
  
  -- Ensure currency is valid
  IF NEW.currency IS NOT NULL AND NEW.currency NOT IN ('USD', 'CAD', 'EUR', 'GBP') THEN
    RAISE EXCEPTION 'Invalid currency code';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply validation trigger to payment_records
DROP TRIGGER IF EXISTS validate_payment_records ON payment_records;
CREATE TRIGGER validate_payment_records
  BEFORE INSERT OR UPDATE ON payment_records
  FOR EACH ROW EXECUTE FUNCTION validate_payment_data();

-- ============================================================================
-- STEP 8: Update RLS policies for consistency
-- ============================================================================

-- Ensure all payment-related tables have proper RLS policies
ALTER TABLE payment_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE booking_forms ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to recreate them consistently
DROP POLICY IF EXISTS "Users can view their own payment records" ON payment_records;
DROP POLICY IF EXISTS "Users can insert their own payment records" ON payment_records;
DROP POLICY IF EXISTS "Users can update their own payment records" ON payment_records;

-- Create consistent RLS policies for payment_records
CREATE POLICY "Users can view their own payment records" ON payment_records
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own payment records" ON payment_records
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Service role can manage all payment records" ON payment_records
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- ============================================================================
-- STEP 9: Add helpful views for payment system
-- ============================================================================

-- Create view for complete payment information
CREATE OR REPLACE VIEW payment_details AS
SELECT 
  pr.id as payment_id,
  pr.user_id,
  pr.service_type,
  pr.amount,
  pr.currency,
  pr.status as payment_status,
  pr.payment_link_id,
  pr.square_payment_id,
  pr.created_at as payment_created_at,
  bf.id as booking_id,
  bf.status as booking_status,
  bf.property_details,
  bf.service_details,
  bf.schedule,
  bf.contact,
  bf.created_at as booking_created_at,
  pl.url as payment_link_url,
  pl.description as payment_description
FROM payment_records pr
LEFT JOIN booking_forms bf ON pr.booking_id = bf.id
LEFT JOIN payment_links pl ON pr.payment_link_id = pl.square_payment_link_id;

-- Grant access to the view
GRANT SELECT ON payment_details TO authenticated;
GRANT SELECT ON payment_details TO service_role;

-- ============================================================================
-- STEP 10: Add helpful functions for payment system
-- ============================================================================

-- Function to get standardized service type
CREATE OR REPLACE FUNCTION get_standardized_service_type(input_service_type text)
RETURNS text AS $$
BEGIN
  RETURN CASE
    WHEN input_service_type = 'Regular House Cleaning' THEN 'residential_regular'
    WHEN input_service_type = 'Deep House Cleaning' THEN 'residential_deep'
    WHEN input_service_type = 'Move-in/Move-out Cleaning' THEN 'residential_move'
    WHEN input_service_type IN ('regular', 'house', 'home') THEN 'residential_regular'
    WHEN input_service_type = 'deep' THEN 'residential_deep'
    WHEN input_service_type LIKE '%residential%' THEN 'residential'
    ELSE input_service_type
  END;
END;
$$ LANGUAGE plpgsql;

-- Function to validate service type
CREATE OR REPLACE FUNCTION is_valid_service_type(service_type text)
RETURNS boolean AS $$
BEGIN
  RETURN service_type IN (
    'office', 'carpet', 'window', 'deep', 'construction', 'sanitization',
    'tile', 'pressure', 'floor', 'pool', 'chimney', 'waste-management',
    'upholstery', 'move-out', 'event', 'post-construction',
    'residential', 'residential_regular', 'residential_deep', 'residential_move', 'regular'
  );
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- VERIFICATION QUERIES (for testing)
-- ============================================================================

-- These queries can be used to verify the migration worked correctly:

-- 1. Check service type distribution
-- SELECT service_type, COUNT(*) FROM booking_forms GROUP BY service_type ORDER BY COUNT(*) DESC;

-- 2. Check payment status distribution
-- SELECT status, COUNT(*) FROM payment_records GROUP BY status;

-- 3. Check for orphaned records
-- SELECT COUNT(*) FROM payment_records WHERE booking_id IS NOT NULL AND booking_id NOT IN (SELECT id FROM booking_forms);

-- 4. Verify foreign key relationships
-- SELECT COUNT(*) FROM payment_records pr LEFT JOIN booking_forms bf ON pr.booking_id = bf.id WHERE pr.booking_id IS NOT NULL AND bf.id IS NULL;

COMMENT ON TABLE payment_records IS 'Stores payment transaction records with standardized schema';
COMMENT ON TABLE payment_links IS 'Stores Square payment link information';
COMMENT ON TABLE booking_forms IS 'Stores service booking information with standardized service types';
COMMENT ON VIEW payment_details IS 'Complete payment and booking information for easy querying';
