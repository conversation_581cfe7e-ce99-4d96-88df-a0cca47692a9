/**
 * Centralized Service Type Registry
 * 
 * This module provides a single source of truth for all service types,
 * their mappings, and validation logic across the entire application.
 * 
 * Evidence: Fixes inconsistencies found across multiple files where
 * different service type formats were used for the same services.
 */

// ============================================================================
// Core Service Type Definitions
// ============================================================================

export interface ServiceType {
  id: string;
  name: string;
  category: 'residential' | 'commercial';
  description: string;
  basePrice?: number;
  isActive: boolean;
  aliases: string[]; // Alternative names/IDs for this service
}

export const SERVICE_TYPES: Record<string, ServiceType> = {
  // Residential Services
  residential_regular: {
    id: 'residential_regular',
    name: 'Regular House Cleaning',
    category: 'residential',
    description: 'Standard residential cleaning service',
    basePrice: 120,
    isActive: true,
    aliases: ['regular', 'house', 'home', 'Regular House Cleaning']
  },
  
  residential_deep: {
    id: 'residential_deep',
    name: 'Deep House Cleaning',
    category: 'residential', 
    description: 'Comprehensive deep cleaning service',
    basePrice: 180,
    isActive: true,
    aliases: ['deep', 'Deep House Cleaning', 'deep-clean']
  },
  
  residential_move: {
    id: 'residential_move',
    name: 'Move-in/Move-out Cleaning',
    category: 'residential',
    description: 'Specialized cleaning for moving',
    basePrice: 200,
    isActive: true,
    aliases: ['move', 'move-out', 'Move-in/Move-out Cleaning', 'moving']
  },
  
  residential: {
    id: 'residential',
    name: 'Residential Cleaning',
    category: 'residential',
    description: 'General residential cleaning service',
    basePrice: 120,
    isActive: true,
    aliases: ['home-cleaning', 'house-cleaning']
  },

  // Commercial Services
  office: {
    id: 'office',
    name: 'Office Cleaning',
    category: 'commercial',
    description: 'Professional office cleaning service',
    basePrice: 150,
    isActive: true,
    aliases: ['commercial', 'business', 'workplace']
  },
  
  carpet: {
    id: 'carpet',
    name: 'Carpet Cleaning',
    category: 'commercial',
    description: 'Professional carpet cleaning service',
    basePrice: 89,
    isActive: true,
    aliases: ['rug', 'carpet-cleaning', 'upholstery']
  },
  
  window: {
    id: 'window',
    name: 'Window Cleaning',
    category: 'commercial',
    description: 'Professional window cleaning service',
    basePrice: 75,
    isActive: true,
    aliases: ['windows', 'glass']
  },
  
  construction: {
    id: 'construction',
    name: 'Post-Construction Cleaning',
    category: 'commercial',
    description: 'Specialized post-construction cleanup',
    basePrice: 300,
    isActive: true,
    aliases: ['post-construction', 'construction-cleanup', 'renovation']
  },
  
  sanitization: {
    id: 'sanitization',
    name: 'Sanitization & Disinfection',
    category: 'commercial',
    description: 'Professional sanitization service',
    basePrice: 200,
    isActive: true,
    aliases: ['disinfection', 'sanitize', 'covid-cleaning']
  },
  
  tile: {
    id: 'tile',
    name: 'Tile & Grout Cleaning',
    category: 'commercial',
    description: 'Specialized tile and grout cleaning',
    basePrice: 150,
    isActive: true,
    aliases: ['grout', 'tile-cleaning', 'bathroom-tile']
  },
  
  pressure: {
    id: 'pressure',
    name: 'Pressure Washing',
    category: 'commercial',
    description: 'High-pressure exterior cleaning',
    basePrice: 199,
    isActive: true,
    aliases: ['pressure-washing', 'power-washing', 'exterior']
  },
  
  floor: {
    id: 'floor',
    name: 'Floor Restoration',
    category: 'commercial',
    description: 'Professional floor restoration service',
    basePrice: 250,
    isActive: true,
    aliases: ['flooring', 'hardwood', 'floor-refinishing']
  },
  
  pool: {
    id: 'pool',
    name: 'Pool Cleaning',
    category: 'commercial',
    description: 'Professional pool cleaning service',
    basePrice: 125,
    isActive: true,
    aliases: ['swimming-pool', 'pool-maintenance']
  },
  
  chimney: {
    id: 'chimney',
    name: 'Chimney Cleaning',
    category: 'commercial',
    description: 'Professional chimney cleaning service',
    basePrice: 175,
    isActive: true,
    aliases: ['fireplace', 'chimney-sweep']
  },
  
  'waste-management': {
    id: 'waste-management',
    name: 'Waste Management',
    category: 'commercial',
    description: 'Professional waste management service',
    basePrice: 100,
    isActive: true,
    aliases: ['waste', 'garbage', 'trash']
  }
};

// ============================================================================
// Service Type Utility Functions
// ============================================================================

/**
 * Normalize any service type input to a standardized service type ID
 */
export function normalizeServiceType(input: string): string {
  if (!input) return 'residential';
  
  const inputLower = input.toLowerCase().trim();
  
  // Direct match
  if (SERVICE_TYPES[input]) {
    return input;
  }
  
  // Search through aliases - exact match first
  for (const [serviceId, serviceType] of Object.entries(SERVICE_TYPES)) {
    if (serviceType.aliases.some(alias => alias.toLowerCase() === inputLower)) {
      return serviceId;
    }
  }

  // Then search for partial matches
  for (const [serviceId, serviceType] of Object.entries(SERVICE_TYPES)) {
    if (serviceType.aliases.some(alias => inputLower.includes(alias.toLowerCase()))) {
      return serviceId;
    }
  }
  
  // Fallback logic for common patterns
  if (inputLower.includes('regular') || inputLower.includes('house')) {
    return 'residential_regular';
  }
  if (inputLower.includes('deep')) {
    return 'residential_deep';
  }
  if (inputLower.includes('move')) {
    return 'residential_move';
  }
  if (inputLower.includes('residential') || inputLower.includes('home')) {
    return 'residential';
  }
  
  // Default fallback
  return 'residential';
}

/**
 * Validate if a service type is valid
 */
export function isValidServiceType(serviceType: string): boolean {
  return !!SERVICE_TYPES[serviceType];
}

/**
 * Get service type information
 */
export function getServiceType(serviceTypeId: string): ServiceType | null {
  return SERVICE_TYPES[serviceTypeId] || null;
}

/**
 * Get all active service types
 */
export function getActiveServiceTypes(): ServiceType[] {
  return Object.values(SERVICE_TYPES).filter(service => service.isActive);
}

/**
 * Get service types by category
 */
export function getServiceTypesByCategory(category: 'residential' | 'commercial'): ServiceType[] {
  return Object.values(SERVICE_TYPES).filter(
    service => service.category === category && service.isActive
  );
}

/**
 * Get display name for a service type
 */
export function getServiceDisplayName(serviceTypeId: string): string {
  const serviceType = SERVICE_TYPES[serviceTypeId];
  return serviceType?.name || 'Cleaning Service';
}

/**
 * Get base price for a service type
 */
export function getServiceBasePrice(serviceTypeId: string): number {
  const serviceType = SERVICE_TYPES[serviceTypeId];
  return serviceType?.basePrice || 120; // Default base price
}

/**
 * Get all valid service type IDs for database constraints
 */
export function getAllValidServiceTypeIds(): string[] {
  return Object.keys(SERVICE_TYPES);
}

/**
 * Convert legacy service type names to current format
 */
export function migrateLegacyServiceType(legacyType: string): string {
  const migrations: Record<string, string> = {
    'Regular House Cleaning': 'residential_regular',
    'Deep House Cleaning': 'residential_deep',
    'Move-in/Move-out Cleaning': 'residential_move',
    'regular': 'residential_regular',
    'deep': 'residential_deep',
    'move': 'residential_move'
  };
  
  return migrations[legacyType] || normalizeServiceType(legacyType);
}

// ============================================================================
// Type Guards and Validation
// ============================================================================

export function isResidentialService(serviceTypeId: string): boolean {
  const serviceType = SERVICE_TYPES[serviceTypeId];
  return serviceType?.category === 'residential';
}

export function isCommercialService(serviceTypeId: string): boolean {
  const serviceType = SERVICE_TYPES[serviceTypeId];
  return serviceType?.category === 'commercial';
}

// ============================================================================
// Export for database constraints
// ============================================================================

/**
 * SQL-ready list of all valid service types for database constraints
 */
export const SQL_SERVICE_TYPE_CONSTRAINT = getAllValidServiceTypeIds()
  .map(id => `'${id}'`)
  .join(', ');

/**
 * Configuration object for easy import in other modules
 */
export const ServiceTypeConfig = {
  types: SERVICE_TYPES,
  normalize: normalizeServiceType,
  validate: isValidServiceType,
  getDisplayName: getServiceDisplayName,
  getBasePrice: getServiceBasePrice,
  isResidential: isResidentialService,
  isCommercial: isCommercialService,
  migrate: migrateLegacyServiceType
} as const;
