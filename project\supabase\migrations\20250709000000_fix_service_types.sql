/*
  # Fix Service Types for Residential Cleaning

  1. Updates
    - Add 'residential' as a valid service type for backward compatibility
    - Ensure all residential service types are properly supported
    
  2. Purpose
    - Fix service type detection issues
    - Support both specific residential types and general 'residential' fallback
*/

-- Drop existing service type constraint
ALTER TABLE booking_forms DROP CONSTRAINT IF EXISTS valid_service_type;

-- Add updated service type constraint with residential fallback
ALTER TABLE booking_forms 
ADD CONSTRAINT valid_service_type CHECK (
  service_type IN (
    'office', 'carpet', 'window', 'deep',
    'construction', 'sanitization', 'tile',
    'pressure', 'floor', 'residential_regular',
    'residential_deep', 'residential_move', 
    'pool', 'waste-management', 'residential'
  )
);

-- Update any existing records that might have invalid service types
UPDATE booking_forms 
SET service_type = 'residential_regular' 
WHERE service_type = 'Regular House Cleaning';

UPDATE booking_forms 
SET service_type = 'residential_deep' 
WHERE service_type = 'Deep House Cleaning';

UPDATE booking_forms 
SET service_type = 'residential' 
WHERE service_type NOT IN (
  'office', 'carpet', 'window', 'deep',
  'construction', 'sanitization', 'tile',
  'pressure', 'floor', 'residential_regular',
  'residential_deep', 'residential_move', 
  'pool', 'waste-management', 'residential'
) AND service_type LIKE '%residential%';
