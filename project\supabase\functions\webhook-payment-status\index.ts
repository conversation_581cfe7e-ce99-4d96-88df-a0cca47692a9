import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "npm:@supabase/supabase-js";
import { crypto } from "https://deno.land/std@0.168.0/crypto/mod.ts";

// TypeScript interfaces for webhook data
interface SquarePaymentData {
  id: string;
  status: string;
  order_id: string;
  amount_money?: {
    amount: number;
    currency: string;
  };
  created_at?: string;
  updated_at?: string;
}

interface WebhookEvent {
  type: string;
  data: SquarePaymentData;
  event_id?: string;
  merchant_id?: string;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
const supabaseKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// Square webhook signature verification
const webhookSignatureKey = Deno.env.get("SQUARE_WEBHOOK_SIGNATURE_KEY") || "";

// Security configuration
const MAX_PAYLOAD_SIZE = 1024 * 1024; // 1MB
const WEBHOOK_TIMEOUT = 30000; // 30 seconds
const MAX_RETRIES = 3;

// Rate limiting (simple in-memory store for demo)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 100;

// Processed webhook IDs to prevent replay attacks
const processedWebhooks = new Set<string>();
const WEBHOOK_ID_TTL = 24 * 60 * 60 * 1000; // 24 hours

// Function to verify webhook signature
async function verifyWebhookSignature(body: string, signature: string, key: string): Promise<boolean> {
  if (!key || !signature) {
    console.warn("Missing webhook signature key or signature header");
    return false;
  }

  try {
    // Remove 'sha256=' prefix if present
    const cleanSignature = signature.replace(/^sha256=/, '');

    // Create HMAC-SHA256 hash
    const encoder = new TextEncoder();
    const keyData = encoder.encode(key);
    const bodyData = encoder.encode(body);

    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    );

    const signature_buffer = await crypto.subtle.sign('HMAC', cryptoKey, bodyData);
    const expectedSignature = Array.from(new Uint8Array(signature_buffer))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');

    // Compare signatures using constant-time comparison
    return constantTimeCompare(cleanSignature, expectedSignature);
  } catch (error) {
    console.error("Error verifying webhook signature:", error);
    return false;
  }
}

// Constant-time string comparison to prevent timing attacks
function constantTimeCompare(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false;
  }

  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }

  return result === 0;
}

// Rate limiting function
function checkRateLimit(clientId: string): boolean {
  const now = Date.now();
  const clientData = rateLimitStore.get(clientId);

  if (!clientData || now > clientData.resetTime) {
    // Reset or initialize rate limit
    rateLimitStore.set(clientId, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW
    });
    return true;
  }

  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false; // Rate limit exceeded
  }

  clientData.count++;
  return true;
}

// Validate webhook payload structure
function validateWebhookPayload(payload: any): boolean {
  if (!payload || typeof payload !== 'object') {
    return false;
  }

  // Required fields
  if (!payload.type || typeof payload.type !== 'string') {
    return false;
  }

  if (!payload.data || typeof payload.data !== 'object') {
    return false;
  }

  // Validate data structure
  const data = payload.data;
  if (!data.id || typeof data.id !== 'string') {
    return false;
  }

  if (!data.status || typeof data.status !== 'string') {
    return false;
  }

  // Validate event type
  const validEventTypes = ['payment.created', 'payment.updated', 'payment.completed', 'payment.failed'];
  if (!validEventTypes.includes(payload.type)) {
    return false;
  }

  // Validate status values
  const validStatuses = ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELED'];
  if (!validStatuses.includes(data.status)) {
    return false;
  }

  return true;
}

// Sanitize webhook data
function sanitizeWebhookData(data: SquarePaymentData): SquarePaymentData {
  return {
    id: sanitizeString(data.id),
    status: sanitizeString(data.status),
    order_id: sanitizeString(data.order_id),
    amount_money: data.amount_money ? {
      amount: typeof data.amount_money.amount === 'number' ? data.amount_money.amount : 0,
      currency: sanitizeString(data.amount_money.currency || 'USD')
    } : undefined,
    created_at: data.created_at ? sanitizeString(data.created_at) : undefined,
    updated_at: data.updated_at ? sanitizeString(data.updated_at) : undefined
  };
}

// Simple string sanitization
function sanitizeString(input: string): string {
  if (typeof input !== 'string') {
    return '';
  }

  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/['"]/g, '') // Remove quotes
    .replace(/[;]/g, '') // Remove semicolons
    .replace(/--/g, '') // Remove SQL comment markers
    .trim()
    .slice(0, 255); // Limit length
}

// Check for replay attacks
function isReplayAttack(webhookId: string, timestamp?: string): boolean {
  // Check if webhook was already processed
  if (processedWebhooks.has(webhookId)) {
    return true;
  }

  // Check timestamp if provided
  if (timestamp) {
    const webhookTime = new Date(timestamp).getTime();
    const now = Date.now();
    const maxAge = 5 * 60 * 1000; // 5 minutes

    if (now - webhookTime > maxAge) {
      return true; // Too old
    }
  }

  // Mark as processed
  processedWebhooks.add(webhookId);

  // Clean up old entries periodically
  if (processedWebhooks.size > 10000) {
    processedWebhooks.clear();
  }

  return false;
}

serve(async (req) => {
  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Square-Signature",
  };

  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers: corsHeaders,
      status: 204,
    });
  }

  // Only allow POST requests
  if (req.method !== "POST") {
    return new Response(
      JSON.stringify({ error: "Method not allowed" }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 405,
      }
    );
  }

  try {
    // Get client IP for rate limiting
    const clientIP = req.headers.get("x-forwarded-for") || req.headers.get("x-real-ip") || "unknown";

    // Check rate limiting
    if (!checkRateLimit(clientIP)) {
      console.warn(`Rate limit exceeded for IP: ${clientIP}`);
      return new Response(
        JSON.stringify({ error: "Rate limit exceeded" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 429,
        }
      );
    }

    const body = await req.text();

    // Check payload size
    if (body.length > MAX_PAYLOAD_SIZE) {
      console.error(`Payload too large: ${body.length} bytes`);
      return new Response(
        JSON.stringify({ error: "Payload too large" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 413,
        }
      );
    }

    console.log("Webhook received from IP:", clientIP);

    // Verify webhook signature (REQUIRED for security)
    const signature = req.headers.get("X-Square-Signature");

    // Always verify signature if key is configured
    if (webhookSignatureKey) {
      if (!signature) {
        console.error("Missing webhook signature header");
        return new Response(
          JSON.stringify({ error: "Missing signature header" }),
          {
            headers: { ...corsHeaders, "Content-Type": "application/json" },
            status: 401,
          }
        );
      }

      const isValidSignature = await verifyWebhookSignature(body, signature, webhookSignatureKey);
      if (!isValidSignature) {
        console.error("Invalid webhook signature");
        return new Response(
          JSON.stringify({ error: "Invalid signature" }),
          {
            headers: { ...corsHeaders, "Content-Type": "application/json" },
            status: 401,
          }
        );
      }

      console.log("Webhook signature verified successfully");
    } else {
      console.warn("⚠️ SECURITY WARNING: Webhook signature verification is disabled. Set SQUARE_WEBHOOK_SIGNATURE_KEY environment variable.");
    }

    let webhookEvent: WebhookEvent;

    try {
      webhookEvent = JSON.parse(body) as WebhookEvent;
    } catch (parseError) {
      console.error("Invalid JSON payload:", parseError);
      return new Response(
        JSON.stringify({ error: "Invalid JSON payload" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    // Validate webhook payload structure
    if (!validateWebhookPayload(webhookEvent)) {
      console.error("Invalid webhook payload structure:", webhookEvent);
      return new Response(
        JSON.stringify({ error: "Invalid payload structure" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    // Check for replay attacks
    const webhookId = `${webhookEvent.data.id}-${webhookEvent.data.updated_at || webhookEvent.data.created_at}`;
    if (isReplayAttack(webhookId, webhookEvent.data.updated_at || webhookEvent.data.created_at)) {
      console.warn("Replay attack detected:", webhookId);
      return new Response(
        JSON.stringify({ error: "Duplicate webhook" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 409,
        }
      );
    }

    // Sanitize webhook data
    const sanitizedData = sanitizeWebhookData(webhookEvent.data);

    console.log("Processing webhook event:", webhookEvent.type, "for payment:", sanitizedData.id);

    // Handle different webhook event types
    if (webhookEvent.type === "payment.created") {
      await handlePaymentCreated(sanitizedData);
    } else if (webhookEvent.type === "payment.updated") {
      await handlePaymentUpdated(sanitizedData);
    } else {
      console.log("Unhandled webhook event type:", webhookEvent.type);
    }

    return new Response(
      JSON.stringify({ success: true }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Webhook processing error:", error);
    
    return new Response(
      JSON.stringify({ error: "Webhook processing failed" }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});

async function handlePaymentCreated(paymentData: SquarePaymentData) {
  console.log("Processing payment created:", paymentData);
  
  try {
    // Update payment record status to 'processing'
    const { error } = await supabase
      .from("payment_records")
      .update({
        status: "processing",
        square_payment_id: paymentData.id,
        updated_at: new Date().toISOString(),
      })
      .eq("payment_link_id", paymentData.order_id);

    if (error) {
      console.error("Error updating payment record:", error);
    } else {
      console.log("Payment record updated to processing");
    }
  } catch (error) {
    console.error("Error in handlePaymentCreated:", error);
  }
}

async function handlePaymentUpdated(paymentData: SquarePaymentData) {
  console.log("Processing payment updated:", paymentData);
  
  try {
    let status = "pending";
    
    // Map Square payment status to our status
    if (paymentData.status === "COMPLETED") {
      status = "completed";
    } else if (paymentData.status === "FAILED") {
      status = "failed";
    } else if (paymentData.status === "CANCELED") {
      status = "cancelled";
    }

    // Update payment record with enhanced metadata
    const { error } = await supabase
      .from("payment_records")
      .update({
        status,
        square_payment_id: paymentData.id,
        updated_at: new Date().toISOString(),
        metadata: {
          square_payment_data: paymentData,
          webhook_processed_at: new Date().toISOString(),
          payment_status_history: [
            {
              status: status,
              timestamp: new Date().toISOString(),
              source: 'webhook'
            }
          ]
        },
      })
      .eq("payment_link_id", paymentData.order_id);

    if (error) {
      console.error("Error updating payment record:", error);
    } else {
      console.log(`Payment record updated to ${status}`);
      
      // If payment is completed, update booking status
      if (status === "completed") {
        await updateBookingStatus(paymentData.order_id);
      }
    }
  } catch (error) {
    console.error("Error in handlePaymentUpdated:", error);
  }
}

async function updateBookingStatus(paymentLinkId: string) {
  try {
    // Find the booking associated with this payment
    const { data: paymentRecord, error: paymentError } = await supabase
      .from("payment_records")
      .select("booking_id")
      .eq("payment_link_id", paymentLinkId)
      .single();

    if (paymentError || !paymentRecord?.booking_id) {
      console.log("No booking found for payment link:", paymentLinkId);
      return;
    }

    // Update booking status to confirmed
    const { error: bookingError } = await supabase
      .from("booking_forms")
      .update({
        status: "confirmed",
        payment_status: "paid",
        updated_at: new Date().toISOString(),
      })
      .eq("id", paymentRecord.booking_id);

    if (bookingError) {
      console.error("Error updating booking status:", bookingError);
    } else {
      console.log("Booking status updated to confirmed");
    }
  } catch (error) {
    console.error("Error updating booking status:", error);
  }
}