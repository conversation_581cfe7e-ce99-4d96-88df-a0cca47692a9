/**
 * Comprehensive Payment System Integration Tests
 * 
 * This test suite validates all the fixes implemented for the payment system:
 * 1. Database schema consistency
 * 2. Service type normalization
 * 3. Pricing calculation accuracy
 * 4. Checkout flow integration
 * 5. End-to-end payment processing
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { 
  normalizeServiceType, 
  isValidServiceType, 
  getServiceDisplayName,
  getServiceBasePrice,
  ServiceTypeConfig 
} from '../lib/services/serviceTypeRegistry';
import { 
  calculatePrice, 
  getQuickPrice, 
  validatePricingInput,
  formatPrice,
  type PricingInput 
} from '../lib/services/pricingService';

// Mock Supabase for testing
vi.mock('../lib/supabase/client', () => ({
  supabase: {
    functions: {
      invoke: vi.fn()
    },
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn()
        }))
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn()
        }))
      })),
      update: vi.fn(() => ({
        eq: vi.fn()
      }))
    }))
  }
}));

describe('Payment System Integration Tests', () => {
  
  describe('Service Type Registry', () => {
    
    it('should normalize legacy service types correctly', () => {
      expect(normalizeServiceType('Regular House Cleaning')).toBe('residential_regular');
      expect(normalizeServiceType('Deep House Cleaning')).toBe('residential_deep');
      expect(normalizeServiceType('Move-in/Move-out Cleaning')).toBe('residential_move');
    });
    
    it('should handle various input formats', () => {
      expect(normalizeServiceType('regular')).toBe('residential_regular');
      expect(normalizeServiceType('deep')).toBe('residential_deep');
      expect(normalizeServiceType('house')).toBe('residential_regular');
      expect(normalizeServiceType('home')).toBe('residential_regular');
      expect(normalizeServiceType('')).toBe('residential');
      expect(normalizeServiceType(undefined as any)).toBe('residential');
    });
    
    it('should validate service types correctly', () => {
      expect(isValidServiceType('residential_regular')).toBe(true);
      expect(isValidServiceType('office')).toBe(true);
      expect(isValidServiceType('carpet')).toBe(true);
      expect(isValidServiceType('invalid_service')).toBe(false);
      expect(isValidServiceType('')).toBe(false);
    });
    
    it('should provide correct display names', () => {
      expect(getServiceDisplayName('residential_regular')).toBe('Regular House Cleaning');
      expect(getServiceDisplayName('office')).toBe('Office Cleaning');
      expect(getServiceDisplayName('invalid')).toBe('Cleaning Service');
    });
    
    it('should provide base prices for all service types', () => {
      expect(getServiceBasePrice('residential_regular')).toBeGreaterThan(0);
      expect(getServiceBasePrice('residential_deep')).toBeGreaterThan(0);
      expect(getServiceBasePrice('office')).toBeGreaterThan(0);
      expect(getServiceBasePrice('invalid')).toBe(120); // Default fallback
    });
  });
  
  describe('Pricing Service', () => {
    
    it('should calculate basic residential pricing correctly', () => {
      const input: PricingInput = {
        serviceType: 'residential_regular',
        propertySize: 1500,
        frequency: 'onetime'
      };
      
      const result = calculatePrice(input);
      
      expect(result.total).toBeGreaterThan(0);
      expect(result.currency).toBe('USD');
      expect(result.breakdown).toHaveLength(2); // Base + Size adjustment
      expect(result.breakdown[0].type).toBe('base');
    });
    
    it('should apply frequency discounts correctly', () => {
      const oneTimeInput: PricingInput = {
        serviceType: 'residential_regular',
        propertySize: 1500,
        frequency: 'onetime'
      };
      
      const weeklyInput: PricingInput = {
        ...oneTimeInput,
        frequency: 'weekly'
      };
      
      const oneTimePrice = calculatePrice(oneTimeInput);
      const weeklyPrice = calculatePrice(weeklyInput);
      
      expect(weeklyPrice.total).toBeLessThan(oneTimePrice.total);
      expect(weeklyPrice.frequencyDiscount).toBeGreaterThan(0);
    });
    
    it('should handle add-ons correctly', () => {
      const baseInput: PricingInput = {
        serviceType: 'residential_regular',
        propertySize: 1500,
        frequency: 'onetime'
      };
      
      const withAddOnsInput: PricingInput = {
        ...baseInput,
        addOns: ['deep-clean', 'windows']
      };
      
      const basePrice = calculatePrice(baseInput);
      const withAddOnsPrice = calculatePrice(withAddOnsInput);
      
      expect(withAddOnsPrice.total).toBeGreaterThan(basePrice.total);
      expect(withAddOnsPrice.addOnsTotal).toBeGreaterThan(0);
    });
    
    it('should validate pricing input correctly', () => {
      const validInput: PricingInput = {
        serviceType: 'residential_regular',
        propertySize: 1500
      };
      
      const invalidInput: PricingInput = {
        serviceType: 'invalid_service',
        frequency: 'invalid_frequency'
      };
      
      expect(validatePricingInput(validInput).isValid).toBe(true);
      expect(validatePricingInput(invalidInput).isValid).toBe(false);
      expect(validatePricingInput(invalidInput).errors).toContain('Invalid service type: invalid_service');
    });
    
    it('should format prices correctly', () => {
      expect(formatPrice(150.50)).toBe('$150.50');
      expect(formatPrice(1000)).toBe('$1,000.00');
    });
    
    it('should handle edge cases gracefully', () => {
      // Very large property
      const largePropertyInput: PricingInput = {
        serviceType: 'residential_regular',
        propertySize: 15000
      };
      
      const result = calculatePrice(largePropertyInput);
      expect(result.total).toBeGreaterThan(0);
      
      // Commercial service
      const commercialInput: PricingInput = {
        serviceType: 'office',
        propertySize: 5000
      };
      
      const commercialResult = calculatePrice(commercialInput);
      expect(commercialResult.total).toBeGreaterThan(0);
    });
  });
  
  describe('Database Schema Compatibility', () => {
    
    it('should ensure all service types are database-compatible', () => {
      const allServiceTypes = Object.keys(ServiceTypeConfig.types);
      
      allServiceTypes.forEach(serviceType => {
        expect(isValidServiceType(serviceType)).toBe(true);
        expect(serviceType).toMatch(/^[a-z_-]+$/); // Only lowercase, underscores, hyphens
        expect(serviceType.length).toBeLessThanOrEqual(50); // Reasonable length limit
      });
    });
    
    it('should handle amount conversion consistently', () => {
      const dollarAmount = 150.75;
      const centsAmount = Math.round(dollarAmount * 100);
      const backToDollars = centsAmount / 100;
      
      expect(centsAmount).toBe(15075);
      expect(backToDollars).toBe(150.75);
    });
  });
  
  describe('Integration Scenarios', () => {
    
    it('should handle complete residential booking flow', () => {
      // Simulate form data from residential booking
      const formData = {
        serviceType: 'Regular House Cleaning',
        propertySize: '2000-3000',
        frequency: 'weekly',
        addOns: ['deep-clean', 'windows']
      };
      
      // Normalize service type
      const normalizedServiceType = normalizeServiceType(formData.serviceType);
      expect(normalizedServiceType).toBe('residential_regular');
      
      // Calculate pricing
      const pricingInput: PricingInput = {
        serviceType: normalizedServiceType,
        propertySize: formData.propertySize,
        frequency: formData.frequency,
        addOns: formData.addOns
      };
      
      const pricing = calculatePrice(pricingInput);
      expect(pricing.total).toBeGreaterThan(0);
      expect(pricing.frequencyDiscount).toBeGreaterThan(0); // Weekly discount applied
      expect(pricing.addOnsTotal).toBeGreaterThan(0); // Add-ons included
    });
    
    it('should handle commercial service booking', () => {
      const formData = {
        serviceType: 'office',
        propertySize: 5000,
        frequency: 'monthly'
      };
      
      const normalizedServiceType = normalizeServiceType(formData.serviceType);
      expect(normalizedServiceType).toBe('office');
      
      const pricing = calculatePrice({
        serviceType: normalizedServiceType,
        propertySize: formData.propertySize,
        frequency: formData.frequency
      });
      
      expect(pricing.total).toBeGreaterThan(0);
    });
    
    it('should handle legacy data migration', () => {
      const legacyServiceTypes = [
        'Regular House Cleaning',
        'Deep House Cleaning',
        'Move-in/Move-out Cleaning'
      ];
      
      legacyServiceTypes.forEach(legacyType => {
        const normalized = normalizeServiceType(legacyType);
        expect(isValidServiceType(normalized)).toBe(true);
        expect(getServiceBasePrice(normalized)).toBeGreaterThan(0);
      });
    });
  });
  
  describe('Error Handling', () => {
    
    it('should handle invalid service types gracefully', () => {
      expect(() => normalizeServiceType('completely_invalid')).not.toThrow();
      expect(normalizeServiceType('completely_invalid')).toBe('residential');
    });
    
    it('should handle missing pricing data gracefully', () => {
      const result = calculatePrice({
        serviceType: 'residential_regular'
        // Missing other fields
      });
      
      expect(result.total).toBeGreaterThan(0);
      expect(result.breakdown).toBeDefined();
    });
    
    it('should validate required fields', () => {
      const validation = validatePricingInput({} as PricingInput);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Service type is required');
    });
  });
});

// Helper function to run database migration tests (if needed)
export function runDatabaseMigrationTests() {
  // These would be run against a test database
  const testQueries = [
    "SELECT service_type, COUNT(*) FROM booking_forms GROUP BY service_type",
    "SELECT status, COUNT(*) FROM payment_records GROUP BY status",
    "SELECT COUNT(*) FROM payment_records WHERE booking_id IS NOT NULL AND booking_id NOT IN (SELECT id FROM booking_forms)"
  ];
  
  return testQueries;
}
